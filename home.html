<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ملكوتك آتٍ - Kingdom Coming</title>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-gold: #D4AF37;
            --primary-dark: #B8860B;
            --secondary-blue: #1E3A8A;
            --text-dark: #1F2937;
            --text-light: #6B7280;
            --white: #FFFFFF;
            --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: var(--gradient-bg);
            min-height: 100vh;
            direction: rtl;
            overflow-x: hidden;
        }



        /* الحاوية الرئيسية */
        .main-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
        }

        /* بطاقة الترحيب */
        .welcome-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 30px;
            padding: 50px 40px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
            text-align: center;
            max-width: 600px;
            width: 100%;
            border: 2px solid rgba(255, 255, 255, 0.3);

        }



        /* الشعار */
        .logo-section {
            margin-bottom: 30px;
        }

        .logo {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, var(--primary-gold), var(--primary-dark));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            box-shadow: 0 15px 30px rgba(212, 175, 55, 0.4);

        }

        .logo i {
            font-size: 50px;
            color: var(--white);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }



        .app-title {
            font-family: 'Amiri', serif;
            font-size: 3rem;
            color: var(--secondary-blue);
            margin-bottom: 10px;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .app-subtitle {
            font-size: 1.2rem;
            color: var(--text-light);
            margin-bottom: 30px;
            font-weight: 500;
        }

        /* الآية المقدسة */
        .bible-verse {
            background: linear-gradient(135deg, var(--secondary-blue), #1e40af);
            color: var(--white);
            padding: 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 25px rgba(30, 58, 138, 0.3);
            position: relative;
            overflow: hidden;
        }

        .bible-verse::before {
            content: '"';
            position: absolute;
            top: -10px;
            right: 20px;
            font-size: 6rem;
            color: rgba(255, 255, 255, 0.2);
            font-family: serif;
        }

        .verse-text {
            font-family: 'Amiri', serif;
            font-size: 1.4rem;
            line-height: 1.6;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .verse-reference {
            font-size: 1rem;
            opacity: 0.9;
            font-style: italic;
        }

        /* رسالة الترحيب */
        .welcome-message {
            margin-bottom: 30px;
        }

        .welcome-title {
            font-family: 'Amiri', serif;
            font-size: 2rem;
            color: var(--secondary-blue);
            margin-bottom: 15px;
            font-weight: 700;
        }

        .welcome-text {
            font-size: 1.1rem;
            color: var(--text-light);
            line-height: 1.6;
            margin-bottom: 20px;
        }

        /* أزرار العمل */
        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-bottom: 30px;
        }

        .btn {
            padding: 18px 30px;
            border: none;
            border-radius: 15px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-gold), var(--primary-dark));
            color: var(--white);
            box-shadow: 0 8px 20px rgba(212, 175, 55, 0.4);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 30px rgba(212, 175, 55, 0.6);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #10b981, #059669);
            color: var(--white);
            box-shadow: 0 8px 20px rgba(16, 185, 129, 0.4);
        }

        .btn-secondary:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 30px rgba(16, 185, 129, 0.6);
        }

        /* رمز الاستجابة */
        .qr-section {
            background: rgba(248, 250, 252, 0.8);
            border-radius: 20px;
            padding: 25px;
            border: 2px dashed var(--primary-gold);
            margin-bottom: 20px;
        }

        .qr-title {
            font-size: 1.1rem;
            color: var(--secondary-blue);
            margin-bottom: 15px;
            font-weight: 600;
        }

        .qr-placeholder {
            width: 150px;
            height: 150px;
            background: var(--white);
            border: 2px solid var(--primary-gold);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 3rem;
            color: var(--primary-gold);
        }

        .qr-text {
            font-size: 0.9rem;
            color: var(--text-light);
            line-height: 1.4;
        }

        /* تذييل */
        .footer {
            margin-top: 20px;
            text-align: center;
        }

        .designer-credit {
            font-size: 0.9rem;
            color: var(--text-light);
            font-style: italic;
        }



        /* استجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            .welcome-card {
                padding: 30px 25px;
                margin: 10px;
            }
            
            .app-title {
                font-size: 2.2rem;
            }
            
            .verse-text {
                font-size: 1.2rem;
            }
            
            .action-buttons {
                gap: 12px;
            }
            
            .btn {
                padding: 15px 25px;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>


    <!-- المحتوى الرئيسي -->
    <div class="main-container">
        <div class="welcome-card">
            <!-- الشعار واسم التطبيق -->
            <div class="logo-section">
                <div class="logo">
                    <i class="fas fa-cross"></i>
                </div>
                <h1 class="app-title">ملكوتك آتٍ</h1>
                <p class="app-subtitle">Kingdom Coming</p>
            </div>

            <!-- الآية المقدسة -->
            <div class="bible-verse">
                <p class="verse-text">"كُنْ أَمِينًا إِلَى الْمَوْتِ، فَسَأُعْطِيَكَ إِكْلِيلَ الْحَيَاةِ"</p>
                <p class="verse-reference">- رؤيا 2: 10</p>
            </div>

            <!-- رسالة الترحيب -->
            <div class="welcome-message">
                <h2 class="welcome-title">سجل دخولك للبدء</h2>
                <p class="welcome-text">
                    ابدأ رحلتك الروحية معنا في برنامج ملكوتك آتٍ<br>
                    رحلة مليئة بالبركات والنعم الروحية
                </p>
            </div>

            <!-- أزرار العمل -->
            <div class="action-buttons">
                <a href="login-new.html" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول للبرنامج
                </a>
                
                <button onclick="showForm()" class="btn btn-secondary">
                    <i class="fas fa-qrcode"></i>
                    التسجيل عن طريق رمز الاستجابة
                </button>
            </div>

            <!-- رمز الاستجابة -->
            <div id="qr-section" class="qr-section" style="display: none;">
                <h3 class="qr-title">امسح رمز الاستجابة للتسجيل السريع</h3>
                <div class="qr-placeholder">
                    <i class="fas fa-qrcode"></i>
                </div>
                <p class="qr-text">
                    استخدم كاميرا هاتفك لمسح الرمز أو اضغط على الزر أدناه للتسجيل
                </p>
                <button onclick="window.location.href='register-form.html'" class="btn btn-primary" style="margin-top: 15px;">
                    <i class="fas fa-edit"></i>
                    املأ النموذج
                </button>
            </div>

            <!-- تذييل -->
            <div class="footer">
                <p class="designer-credit">تصميم وتطوير: بيشوي مراد</p>
            </div>
        </div>
    </div>

    <script>
        function showForm() {
            const qrSection = document.getElementById('qr-section');
            if (qrSection.style.display === 'none') {
                qrSection.style.display = 'block';
                qrSection.scrollIntoView({ behavior: 'smooth' });
            } else {
                qrSection.style.display = 'none';
            }
        }

        // تأثير تحميل الصفحة
        window.addEventListener('load', function() {
            document.body.style.opacity = '0';
            document.body.style.transition = 'opacity 0.5s ease-in-out';
            setTimeout(() => {
                document.body.style.opacity = '1';
            }, 100);
        });
    </script>
</body>
</html>
