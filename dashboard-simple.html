<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ملكوتك آتٍ - الصفحة الرئيسية</title>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-gold: #D4AF37;
            --primary-dark: #B8860B;
            --secondary-blue: #1E3A8A;
            --text-dark: #1F2937;
            --text-light: #6B7280;
            --white: #FFFFFF;
            --background: #f8fafc;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: var(--background);
            min-height: 100vh;
            direction: rtl;
            color: var(--text-dark);
        }

        .header {
            background: linear-gradient(135deg, var(--primary-gold), var(--primary-dark));
            color: var(--white);
            padding: 20px 0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo-mini {
            width: 60px;
            height: 60px;
            background: var(--white);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            color: var(--primary-gold);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .app-info h1 {
            font-family: 'Amiri', serif;
            font-size: 2rem;
            margin-bottom: 5px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .app-info p {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .user-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: 3px solid var(--white);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .user-details {
            display: flex;
            flex-direction: column;
        }

        .user-name {
            font-size: 0.95rem;
            font-weight: 600;
            margin-bottom: 2px;
        }

        .user-email {
            font-size: 0.8rem;
            opacity: 0.8;
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            color: var(--white);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 10px 15px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .main-content {
            max-width: 1200px;
            margin: 40px auto;
            padding: 0 20px;
        }

        .bible-verse-section {
            background: linear-gradient(135deg, var(--secondary-blue), #1e40af);
            color: var(--white);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(30, 58, 138, 0.3);
        }

        .verse-icon {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 28px;
        }

        .verse-text {
            font-family: 'Amiri', serif;
            font-size: 1.8rem;
            font-weight: 700;
            line-height: 1.4;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .verse-reference {
            font-size: 1.1rem;
            opacity: 0.9;
            font-weight: 600;
        }

        .welcome-section {
            background: var(--white);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .fire-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .main-title {
            font-family: 'Amiri', serif;
            font-size: 2.2rem;
            color: var(--secondary-blue);
            margin-bottom: 20px;
            font-weight: 700;
            line-height: 1.3;
        }

        .launch-date {
            background: linear-gradient(135deg, var(--primary-gold), var(--primary-dark));
            color: var(--white);
            padding: 15px 25px;
            border-radius: 25px;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(212, 175, 55, 0.4);
        }

        .subtitle {
            font-size: 1.2rem;
            color: var(--text-light);
            line-height: 1.6;
            margin-bottom: 30px;
        }

        .features-section {
            background: var(--white);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .features-title {
            font-family: 'Amiri', serif;
            font-size: 1.8rem;
            color: var(--secondary-blue);
            text-align: center;
            margin-bottom: 30px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .feature-card {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border: 2px solid transparent;
            border-radius: 16px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            border-color: var(--primary-gold);
            box-shadow: 0 8px 25px rgba(212, 175, 55, 0.2);
        }

        .feature-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }

        .feature-card h3 {
            font-size: 1.2rem;
            color: var(--secondary-blue);
            margin-bottom: 10px;
            font-weight: 600;
        }

        .feature-card p {
            color: var(--text-light);
            font-size: 0.95rem;
            line-height: 1.4;
        }

        .daily-message {
            background: linear-gradient(135deg, #10b981, #059669);
            color: var(--white);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
        }

        .message-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }

        .daily-message p {
            font-size: 1.3rem;
            font-weight: 600;
            line-height: 1.5;
        }

        .join-section {
            background: var(--white);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border: 3px solid var(--primary-gold);
        }

        .join-icon {
            font-size: 2rem;
            margin-bottom: 15px;
        }

        .join-section h3 {
            font-family: 'Amiri', serif;
            font-size: 1.6rem;
            color: var(--secondary-blue);
            margin-bottom: 20px;
        }

        .kingdom-message {
            background: linear-gradient(135deg, var(--primary-gold), var(--primary-dark));
            color: var(--white);
            padding: 20px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            margin-top: 20px;
        }

        .crown-icon {
            font-size: 1.8rem;
        }

        .kingdom-message p {
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0;
        }

        .follow-section {
            background: linear-gradient(135deg, #6366f1, #4f46e5);
            color: var(--white);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
        }

        .follow-section h3 {
            font-family: 'Amiri', serif;
            font-size: 1.6rem;
            margin-bottom: 15px;
        }

        .follow-section p {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 25px;
            opacity: 0.9;
        }

        .social-links {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .whatsapp-btn, .channel-btn {
            padding: 15px 25px;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .whatsapp-btn {
            background: #25d366;
            color: var(--white);
        }

        .whatsapp-btn:hover {
            background: #128c7e;
            transform: translateY(-2px);
        }

        .channel-btn {
            background: var(--white);
            color: #6366f1;
        }

        .channel-btn:hover {
            background: #f1f5f9;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <!-- الهيدر -->
    <header class="header">
        <div class="header-content">
            <div class="logo-section">
                <div class="logo-mini">
                    <i class="fas fa-cross"></i>
                </div>
                <div class="app-info">
                    <h1>ملكوتك آتٍ</h1>
                    <p>Kingdom Coming</p>
                </div>
            </div>
            
            <div class="user-section">
                <div class="user-info">
                    <img id="userAvatar" class="user-avatar" src="https://via.placeholder.com/50x50/D4AF37/ffffff?text=👤" alt="صورة المستخدم">
                    <div class="user-details">
                        <div class="user-name" id="userName">مرحباً بك</div>
                        <div class="user-email" id="userEmail"><EMAIL></div>
                    </div>
                </div>
                <button class="logout-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    خروج
                </button>
            </div>
        </div>
    </header>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <!-- آية مميزة -->
        <div class="bible-verse-section">
            <div class="verse-icon">
                <i class="fas fa-cross"></i>
            </div>
            <h2 class="verse-text">"كُنْ أَمِينًا إِلَى الْمَوْتِ، فَسَأُعْطِيَكَ إِكْلِيلَ الْحَيَاةِ"</h2>
            <p class="verse-reference">- رؤيا 2: 10</p>
        </div>

        <!-- ترحيب مميز -->
        <div class="welcome-section">
            <div class="fire-icon">
                🔥
            </div>
            <h1 class="main-title">برنامج ملكوتك آت - بداية جديدة مع كل يوم</h1>
            <div class="launch-date">
                <i class="fas fa-calendar-alt"></i>
                <span>انتظرونا يوم 27/7/2025</span>
            </div>
            <p class="subtitle">رحلة روحية مختلفة… في كل يوم جديد هتلاقي كلمة تلمسك، وترنيمة ترفعك، وتأمل يرويك!</p>
        </div>

        <!-- مميزات البرنامج -->
        <div class="features-section">
            <h2 class="features-title">📖 مميزات برنامج ملكوتك آت:</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">📿</div>
                    <h3>الأجبية كاملة</h3>
                    <p>صلوات الكنيسة اليومية</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">👑</div>
                    <h3>السنكسار اليومي</h3>
                    <p>سير القديسين</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📖</div>
                    <h3>الكتاب المقدس كامل</h3>
                    <p>بتقسيم واضح</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">✨</div>
                    <h3>آية يومية</h3>
                    <p>تلهمك وتفتح يومك بكلمة حياة</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🕊️</div>
                    <h3>أقوال آباء</h3>
                    <p>عميقة وأصيلة</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">💝</div>
                    <h3>قطعة روحية حسب اليوم</h3>
                    <p>لكل يوم طعمه</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">💌</div>
                    <h3>رسائل يومية خاصة</h3>
                    <p>تلمس قلبك وتفكرك بالسماء</p>
                </div>
            </div>
        </div>

        <!-- رسالة يومية -->
        <div class="daily-message">
            <div class="message-icon">📲</div>
            <p>كل ده هيجيلك برسالة واحدة كل يوم... بسيطة، منظمة، ومليانة نعمة!</p>
        </div>

        <!-- دعوة للانضمام -->
        <div class="join-section">
            <div class="join-icon">📌</div>
            <h3>انضم لينا وابدأ رحلة روحك للملكوت...</h3>
            <div class="kingdom-message">
                <div class="crown-icon">👑</div>
                <p>ملكوتك آت... علشان قلبك يكون للرب دايمًا</p>
            </div>
        </div>

        <!-- متابعة التحديثات -->
        <div class="follow-section">
            <h3>تابعنا علي جروب وإدارة البرنامج</h3>
            <p>لمتابعة كل جديد أو تحديث يحصل في التطبيق هتلاقي رسالة هنا في القناة أو الجروب تبع الواتساب</p>
            <div class="social-links">
                <button class="whatsapp-btn" onclick="openWhatsApp()">
                    <i class="fab fa-whatsapp"></i>
                    جروب الواتساب
                </button>
                <button class="channel-btn" onclick="openChannel()">
                    <i class="fas fa-bullhorn"></i>
                    قناة التحديثات
                </button>
            </div>
        </div>
    </main>

    <script>
        // التحقق من تسجيل الدخول
        function checkLogin() {
            const isLoggedIn = localStorage.getItem('isLoggedIn');
            const userEmail = localStorage.getItem('userEmail');
            const userName = localStorage.getItem('userName');
            const userPicture = localStorage.getItem('userPicture');

            if (!isLoggedIn || isLoggedIn !== 'true') {
                window.location.href = 'login-simple.html';
                return;
            }

            // عرض بيانات المستخدم
            if (userEmail) {
                document.getElementById('userEmail').textContent = userEmail;
            }

            if (userName) {
                document.getElementById('userName').textContent = userName;
            } else {
                document.getElementById('userName').textContent = userEmail ? userEmail.split('@')[0] : 'مستخدم';
            }

            if (userPicture) {
                document.getElementById('userAvatar').src = userPicture;
            }
        }

        // تسجيل الخروج
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                localStorage.removeItem('isLoggedIn');
                localStorage.removeItem('userEmail');
                localStorage.removeItem('userName');
                localStorage.removeItem('userPicture');
                localStorage.removeItem('loginMethod');
                window.location.href = 'index.html';
            }
        }

        // فتح جروب الواتساب
        function openWhatsApp() {
            // يمكنك استبدال هذا الرابط برابط جروب الواتساب الحقيقي
            window.open('https://chat.whatsapp.com/your-group-link', '_blank');
        }

        // فتح قناة التحديثات
        function openChannel() {
            // يمكنك استبدال هذا الرابط برابط القناة الحقيقية
            window.open('https://t.me/your-channel', '_blank');
        }

        // تشغيل التحقق عند تحميل الصفحة
        window.onload = checkLogin;
    </script>
</body>
</html>
