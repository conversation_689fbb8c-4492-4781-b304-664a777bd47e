# إعداد EmailJS لإرسال كود التحقق

## خطوات الإعداد:

### 1. إنشاء حساب EmailJS
1. اذهب إلى: https://www.emailjs.com/
2. اضغط "Sign Up" وأنشئ حساب جديد
3. تأكد من بريدك الإلكتروني

### 2. <PERSON><PERSON>د<PERSON> خدمة الإيميل
1. من لوحة التحكم، اضغط "Email Services"
2. اضغط "Add New Service"
3. اختر Gmail أو أي خدمة إيميل تفضلها
4. اتبع التعليمات لربط حسابك
5. احفظ الـ Service ID

### 3. إنشاء قالب الإيميل
1. اضغط "Email Templates"
2. اضغط "Create New Template"
3. استخدم هذا القالب:

```
الموضوع: كود التحقق - {{app_name}}

مرحباً،

كود التحقق الخاص بك هو: {{verification_code}}

هذا الكود صالح لمدة 10 دقائق فقط.

إذا لم تطلب هذا الكود، يرجى تجاهل هذه الرسالة.

مع تحيات فريق ملكوتك آتٍ
```

4. احفظ الـ Template ID

### 4. الحصول على Public Key
1. اذهب إلى "Account" > "General"
2. انسخ الـ Public Key

### 5. تحديث الكود
في ملف `login.html`، ابحث عن هذه الأسطر وغيرها:

```javascript
emailjs.init("YOUR_PUBLIC_KEY"); // ضع Public Key هنا

emailjs.send('YOUR_SERVICE_ID', 'YOUR_TEMPLATE_ID', templateParams)
// ضع Service ID و Template ID هنا
```

### مثال على الكود المحدث:
```javascript
emailjs.init("user_xxxxxxxxxx"); // Public Key الخاص بك

emailjs.send('service_xxxxxxxx', 'template_xxxxxxxx', templateParams)
// Service ID و Template ID الخاص بك
```

## ملاحظات مهمة:

### الحد المجاني:
- EmailJS يسمح بـ 200 إيميل مجاناً شهرياً
- إذا احتجت أكثر، يمكنك الترقية للخطة المدفوعة

### الأمان:
- Public Key آمن للاستخدام في المتصفح
- لا تشارك Service ID أو Template ID مع أحد

### اختبار النظام:
1. بعد الإعداد، جرب إدخال إيميلك
2. تحقق من صندوق الوارد (وأيضاً Spam)
3. أدخل الكود المستلم

### حل المشاكل الشائعة:

**لا يصل الإيميل:**
- تحقق من إعدادات Spam
- تأكد من صحة Service ID و Template ID
- تحقق من حالة الخدمة في لوحة تحكم EmailJS

**خطأ في الإرسال:**
- تحقق من Public Key
- تأكد من اتصال الإنترنت
- راجع console في المتصفح للأخطاء

## بدائل أخرى:

إذا واجهت مشاكل مع EmailJS، يمكنك استخدام:
- **Formspree**: https://formspree.io/
- **Netlify Forms**: للمواقع المستضافة على Netlify
- **Firebase Auth**: نظام مصادقة متكامل

## الدعم:
إذا احتجت مساعدة في الإعداد، يمكنك:
1. مراجعة وثائق EmailJS: https://www.emailjs.com/docs/
2. التواصل مع الدعم الفني لـ EmailJS
