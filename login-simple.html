<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - ملكوتك آتٍ</title>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-gold: #D4AF37;
            --primary-dark: #B8860B;
            --secondary-blue: #1E3A8A;
            --text-dark: #1F2937;
            --text-light: #6B7280;
            --white: #FFFFFF;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            direction: rtl;
        }

        .login-container {
            background: var(--white);
            border-radius: 24px;
            padding: 40px 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, var(--primary-gold), var(--primary-dark));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            color: var(--white);
        }

        .login-title {
            font-family: 'Amiri', serif;
            font-size: 1.8rem;
            color: var(--secondary-blue);
            margin-bottom: 30px;
        }

        .google-btn {
            width: 100%;
            background: var(--white);
            border: 2px solid #dadce0;
            border-radius: 12px;
            padding: 15px 20px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            color: #3c4043;
            margin-bottom: 20px;
        }

        .google-btn:hover {
            background: #f8f9fa;
            border-color: #c1c7cd;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .google-logo {
            width: 20px;
            height: 20px;
        }

        .selected-account {
            background: #f8fafc;
            border: 2px solid var(--primary-gold);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            display: none;
        }

        .account-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
            padding: 10px;
            background: var(--white);
            border-radius: 12px;
        }

        .account-picture {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: 2px solid var(--primary-gold);
        }

        .account-details {
            flex: 1;
            margin: 0 15px;
            text-align: right;
        }

        .account-name {
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 4px;
        }

        .account-email {
            color: var(--text-light);
            font-size: 0.9rem;
        }

        .change-btn {
            background: var(--primary-gold);
            color: var(--white);
            border: none;
            border-radius: 8px;
            width: 40px;
            height: 40px;
            cursor: pointer;
            font-size: 14px;
        }

        .proceed-btn {
            width: 100%;
            background: linear-gradient(135deg, #10b981, #059669);
            color: var(--white);
            border: none;
            border-radius: 12px;
            padding: 15px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .proceed-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(16, 185, 129, 0.4);
        }

        .welcome-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .welcome-modal-content {
            background: var(--white);
            border-radius: 20px;
            width: 90%;
            max-width: 450px;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid var(--primary-gold);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .welcome-header h3 {
            color: var(--secondary-blue);
            font-family: 'Amiri', serif;
            font-size: 1.5rem;
            margin-bottom: 20px;
        }

        .welcome-message {
            color: var(--text-dark);
            font-size: 1.1rem;
            margin-bottom: 8px;
            font-weight: 600;
        }

        .registration-note {
            color: var(--text-light);
            font-size: 0.9rem;
            margin-bottom: 25px;
            font-style: italic;
        }

        .email-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 1rem;
            margin-bottom: 15px;
            direction: rtl;
            text-align: right;
        }

        .email-input:focus {
            outline: none;
            border-color: var(--primary-gold);
        }

        .proceed-email-btn {
            width: 100%;
            background: linear-gradient(135deg, var(--primary-gold), var(--primary-dark));
            color: var(--white);
            border: none;
            border-radius: 12px;
            padding: 15px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .close-welcome-btn {
            position: absolute;
            top: 15px;
            left: 15px;
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: var(--text-light);
            width: 35px;
            height: 35px;
            border-radius: 50%;
        }

        .back-button {
            position: absolute;
            top: 20px;
            right: 20px;
            background: var(--primary-gold);
            color: var(--white);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="goBack()">
        <i class="fas fa-arrow-right"></i>
    </button>

    <div class="login-container">
        <div class="logo">
            <i class="fas fa-cross"></i>
        </div>
        
        <h2 class="login-title">تسجيل الدخول - ملكوتك آتٍ</h2>

        <!-- زر تسجيل الدخول بجوجل -->
        <button onclick="showEmailPrompt()" class="google-btn">
            <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" class="google-logo">
            تسجيل الدخول باستخدام Google
        </button>

        <!-- عرض الحساب المختار -->
        <div id="selected-account" class="selected-account">
            <div class="account-info">
                <img id="account-picture" src="" alt="صورة الحساب" class="account-picture">
                <div class="account-details">
                    <div id="account-name" class="account-name"></div>
                    <div id="account-email" class="account-email"></div>
                </div>
                <button onclick="changeAccount()" class="change-btn">
                    <i class="fas fa-exchange-alt"></i>
                </button>
            </div>
            <button onclick="proceedToApp()" class="proceed-btn">
                <i class="fas fa-sign-in-alt"></i>
                دخول للبرنامج
            </button>
        </div>
    </div>

    <!-- نافذة الترحيب -->
    <div id="welcome-modal" class="welcome-modal">
        <div class="welcome-modal-content">
            <div class="welcome-header">
                <div class="loading-spinner"></div>
                <h3>مرحباً بك في ملكوتك آتٍ</h3>
            </div>
            <div class="welcome-body">
                <p class="welcome-message">أدخل بريدك الإلكتروني للدخول</p>
                <p class="registration-note">(التسجيل يحتاج مرة واحدة فقط لأول مرة)</p>
                
                <div class="email-input-section">
                    <input type="email" id="user-email" placeholder="أدخل إيميل Google الخاص بك" class="email-input" required>
                    <button onclick="proceedWithEmail()" class="proceed-email-btn">
                        <i class="fab fa-google"></i>
                        متابعة
                    </button>
                </div>
                
                <button onclick="closeWelcomeModal()" class="close-welcome-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>

    <script>
        window.tempUserData = null;

        function goBack() {
            window.location.href = 'index.html';
        }

        function showEmailPrompt() {
            document.getElementById('welcome-modal').style.display = 'flex';
            setTimeout(() => {
                document.getElementById('user-email').focus();
            }, 300);
        }

        function proceedWithEmail() {
            const email = document.getElementById('user-email').value;
            if (email && email.includes('@')) {
                const name = email.split('@')[0].replace(/\./g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                const userData = {
                    email: email,
                    name: name,
                    picture: `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=4285f4&color=fff&size=128`
                };
                
                closeWelcomeModal();
                showSelectedAccount(userData);
                window.tempUserData = userData;
            } else {
                alert('يرجى إدخال إيميل صحيح');
            }
        }

        function closeWelcomeModal() {
            document.getElementById('welcome-modal').style.display = 'none';
        }

        function showSelectedAccount(userData) {
            document.getElementById('account-picture').src = userData.picture;
            document.getElementById('account-name').textContent = userData.name;
            document.getElementById('account-email').textContent = userData.email;
            document.getElementById('selected-account').style.display = 'block';
        }

        function changeAccount() {
            document.getElementById('selected-account').style.display = 'none';
            window.tempUserData = null;
        }

        function proceedToApp() {
            if (window.tempUserData) {
                const userData = window.tempUserData;
                
                localStorage.setItem('userEmail', userData.email);
                localStorage.setItem('userName', userData.name);
                localStorage.setItem('userPicture', userData.picture);
                localStorage.setItem('isLoggedIn', 'true');
                localStorage.setItem('loginMethod', 'google');

                alert(`مرحباً ${userData.name}! تم تسجيل الدخول بنجاح في ملكوتك آتٍ`);
                
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 1000);
            }
        }

        // التحقق من تسجيل الدخول المسبق
        window.onload = function() {
            if (localStorage.getItem('isLoggedIn') === 'true') {
                window.location.href = 'dashboard.html';
            }
        }
    </script>
</body>
</html>
