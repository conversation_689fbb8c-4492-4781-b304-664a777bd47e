<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ملكوتك آتٍ - الصفحة الرئيسية</title>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-gold: #D4AF37;
            --primary-dark: #B8860B;
            --secondary-blue: #1E3A8A;
            --text-dark: #1F2937;
            --text-light: #6B7280;
            --white: #FFFFFF;
            --background: #f8fafc;
            --gradient-header: linear-gradient(135deg, var(--primary-gold), var(--primary-dark));
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: var(--background);
            min-height: 100vh;
            direction: rtl;
            color: var(--text-dark);
            overflow-x: hidden;
        }



        /* الهيدر */
        .header {
            background: var(--gradient-header);
            color: var(--white);
            padding: 25px 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            position: relative;
            overflow: hidden;
        }



        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .logo-mini {
            width: 70px;
            height: 70px;
            background: var(--white);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 35px;
            color: var(--primary-gold);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .app-info h1 {
            font-family: 'Amiri', serif;
            font-size: 2.5rem;
            margin-bottom: 8px;
            text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.4);
            font-weight: 700;
        }

        .app-info p {
            font-size: 1.1rem;
            opacity: 0.95;
            font-weight: 500;
        }

        .user-section {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            padding: 12px 20px;
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .user-avatar {
            width: 55px;
            height: 55px;
            border-radius: 50%;
            border: 3px solid var(--white);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .user-details {
            display: flex;
            flex-direction: column;
        }

        .user-name {
            font-size: 1.1rem;
            font-weight: 700;
            margin-bottom: 3px;
        }

        .user-email {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            color: var(--white);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            padding: 12px 20px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
        }

        /* المحتوى الرئيسي */
        .main-content {
            max-width: 1200px;
            margin: 50px auto;
            padding: 0 25px;
        }

        /* آية مميزة */
        .bible-verse-section {
            background: linear-gradient(135deg, var(--secondary-blue), #1e40af);
            color: var(--white);
            border-radius: 25px;
            padding: 50px;
            text-align: center;
            margin-bottom: 40px;
            box-shadow: 0 15px 40px rgba(30, 58, 138, 0.4);
            position: relative;
            overflow: hidden;

        }



        .verse-icon {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 25px;
            font-size: 35px;
            position: relative;
            z-index: 2;
        }

        .verse-text {
            font-family: 'Amiri', serif;
            font-size: 2.2rem;
            font-weight: 700;
            line-height: 1.5;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            position: relative;
            z-index: 2;
        }

        .verse-reference {
            font-size: 1.3rem;
            opacity: 0.95;
            font-weight: 600;
            position: relative;
            z-index: 2;
        }

        /* ترحيب مميز */
        .welcome-section {
            background: var(--white);
            border-radius: 25px;
            padding: 50px;
            text-align: center;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
            margin-bottom: 40px;
            border: 3px solid transparent;
            background-clip: padding-box;
            position: relative;

        }

        .fire-icon {
            font-size: 4rem;
            margin-bottom: 25px;
        }

        .main-title {
            font-family: 'Amiri', serif;
            font-size: 2.8rem;
            color: var(--secondary-blue);
            margin-bottom: 25px;
            font-weight: 700;
            line-height: 1.3;
            background: linear-gradient(135deg, var(--secondary-blue), #3b82f6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .launch-date {
            background: var(--gradient-header);
            color: var(--white);
            padding: 20px 35px;
            border-radius: 30px;
            display: inline-flex;
            align-items: center;
            gap: 15px;
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(212, 175, 55, 0.5);
        }

        .subtitle {
            font-size: 1.4rem;
            color: var(--text-light);
            line-height: 1.7;
            margin-bottom: 35px;
            font-weight: 500;
        }

        /* مميزات البرنامج */
        .features-section {
            background: var(--white);
            border-radius: 25px;
            padding: 50px;
            margin-bottom: 40px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);

        }

        .features-title {
            font-family: 'Amiri', serif;
            font-size: 2.2rem;
            color: var(--secondary-blue);
            text-align: center;
            margin-bottom: 40px;
            font-weight: 700;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 25px;
        }

        .feature-card {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border: 3px solid transparent;
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }



        .feature-card:hover {
            transform: translateY(-8px) scale(1.02);
            border-color: var(--primary-gold);
            box-shadow: 0 15px 40px rgba(212, 175, 55, 0.3);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
        }

        .feature-card h3 {
            font-size: 1.4rem;
            color: var(--secondary-blue);
            margin-bottom: 12px;
            font-weight: 700;
        }

        .feature-card p {
            color: var(--text-light);
            font-size: 1.1rem;
            line-height: 1.5;
            font-weight: 500;
        }

        /* رسالة يومية */
        .daily-message {
            background: linear-gradient(135deg, #10b981, #059669);
            color: var(--white);
            border-radius: 25px;
            padding: 40px;
            text-align: center;
            margin-bottom: 40px;
            box-shadow: 0 15px 40px rgba(16, 185, 129, 0.4);

            position: relative;
            overflow: hidden;
        }



        .message-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            position: relative;
            z-index: 2;
        }

        .daily-message p {
            font-size: 1.5rem;
            font-weight: 700;
            line-height: 1.6;
            position: relative;
            z-index: 2;
        }

        /* دعوة للانضمام */
        .join-section {
            background: var(--white);
            border-radius: 25px;
            padding: 50px;
            text-align: center;
            margin-bottom: 40px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
            border: 4px solid var(--primary-gold);

        }

        .join-icon {
            font-size: 2.5rem;
            margin-bottom: 20px;
        }

        .join-section h3 {
            font-family: 'Amiri', serif;
            font-size: 2rem;
            color: var(--secondary-blue);
            margin-bottom: 25px;
            font-weight: 700;
        }

        .kingdom-message {
            background: var(--gradient-header);
            color: var(--white);
            padding: 25px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            margin-top: 25px;
            box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4);
        }

        .crown-icon {
            font-size: 2.2rem;
        }

        .kingdom-message p {
            font-size: 1.4rem;
            font-weight: 700;
            margin: 0;
        }

        /* متابعة التحديثات */
        .follow-section {
            background: linear-gradient(135deg, #6366f1, #4f46e5);
            color: var(--white);
            border-radius: 25px;
            padding: 50px;
            text-align: center;
            box-shadow: 0 15px 40px rgba(99, 102, 241, 0.4);

        }

        .follow-section h3 {
            font-family: 'Amiri', serif;
            font-size: 2rem;
            margin-bottom: 20px;
            font-weight: 700;
        }

        .follow-section p {
            font-size: 1.3rem;
            line-height: 1.7;
            margin-bottom: 35px;
            opacity: 0.95;
            font-weight: 500;
        }

        .social-links {
            display: flex;
            gap: 25px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .whatsapp-btn, .channel-btn {
            padding: 18px 30px;
            border: none;
            border-radius: 18px;
            font-size: 1.2rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.4s ease;
            display: flex;
            align-items: center;
            gap: 12px;
            position: relative;
            overflow: hidden;
        }



        .whatsapp-btn {
            background: #25d366;
            color: var(--white);
            box-shadow: 0 8px 25px rgba(37, 211, 102, 0.4);
        }

        .whatsapp-btn:hover {
            background: #128c7e;
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(37, 211, 102, 0.6);
        }

        .channel-btn {
            background: var(--white);
            color: #6366f1;
            box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
        }

        .channel-btn:hover {
            background: #f1f5f9;
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(255, 255, 255, 0.5);
        }

        /* استجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }
            
            .user-section {
                flex-direction: column;
                gap: 15px;
            }
            
            .main-content {
                padding: 0 15px;
                margin: 30px auto;
            }
            
            .bible-verse-section,
            .welcome-section,
            .features-section,
            .daily-message,
            .join-section,
            .follow-section {
                padding: 30px 20px;
            }
            
            .app-info h1 {
                font-size: 2rem;
            }
            
            .verse-text {
                font-size: 1.6rem;
            }
            
            .main-title {
                font-size: 2.2rem;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .social-links {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>


    <!-- الهيدر -->
    <header class="header">
        <div class="header-content">
            <div class="logo-section">
                <div class="logo-mini">
                    <i class="fas fa-cross"></i>
                </div>
                <div class="app-info">
                    <h1>ملكوتك آتٍ</h1>
                    <p>Kingdom Coming</p>
                </div>
            </div>
            
            <div class="user-section">
                <div class="user-info">
                    <img id="userAvatar" class="user-avatar" src="https://via.placeholder.com/55x55/D4AF37/ffffff?text=👤" alt="صورة المستخدم">
                    <div class="user-details">
                        <div class="user-name" id="userName">مرحباً بك</div>
                        <div class="user-email" id="userEmail"><EMAIL></div>
                    </div>
                </div>
                <button class="logout-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    خروج
                </button>
            </div>
        </div>
    </header>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <!-- آية مميزة -->
        <div class="bible-verse-section">
            <div class="verse-icon">
                <i class="fas fa-cross"></i>
            </div>
            <h2 class="verse-text">"كُنْ أَمِينًا إِلَى الْمَوْتِ، فَسَأُعْطِيَكَ إِكْلِيلَ الْحَيَاةِ"</h2>
            <p class="verse-reference">- رؤيا 2: 10</p>
        </div>

        <!-- ترحيب مميز -->
        <div class="welcome-section">
            <div class="fire-icon">
                🔥
            </div>
            <h1 class="main-title">برنامج ملكوتك آت - بداية جديدة مع كل يوم</h1>
            <div class="launch-date">
                <i class="fas fa-calendar-alt"></i>
                <span>انتظرونا يوم 27/7/2025</span>
            </div>
            <p class="subtitle">رحلة روحية مختلفة… في كل يوم جديد هتلاقي كلمة تلمسك، وترنيمة ترفعك، وتأمل يرويك!</p>
        </div>

        <!-- مميزات البرنامج -->
        <div class="features-section">
            <h2 class="features-title">📖 مميزات برنامج ملكوتك آت:</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">📿</div>
                    <h3>الأجبية كاملة</h3>
                    <p>صلوات الكنيسة اليومية</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">👑</div>
                    <h3>السنكسار اليومي</h3>
                    <p>سير القديسين</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📖</div>
                    <h3>الكتاب المقدس كامل</h3>
                    <p>بتقسيم واضح</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">✨</div>
                    <h3>آية يومية</h3>
                    <p>تلهمك وتفتح يومك بكلمة حياة</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🕊️</div>
                    <h3>أقوال آباء</h3>
                    <p>عميقة وأصيلة</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">💝</div>
                    <h3>قطعة روحية حسب اليوم</h3>
                    <p>لكل يوم طعمه</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">💌</div>
                    <h3>رسائل يومية خاصة</h3>
                    <p>تلمس قلبك وتفكرك بالسماء</p>
                </div>
            </div>
        </div>

        <!-- رسالة يومية -->
        <div class="daily-message">
            <div class="message-icon">📲</div>
            <p>كل ده هيجيلك برسالة واحدة كل يوم... بسيطة، منظمة، ومليانة نعمة!</p>
        </div>

        <!-- دعوة للانضمام -->
        <div class="join-section">
            <div class="join-icon">📌</div>
            <h3>انضم لينا وابدأ رحلة روحك للملكوت...</h3>
            <div class="kingdom-message">
                <div class="crown-icon">👑</div>
                <p>ملكوتك آت... علشان قلبك يكون للرب دايمًا</p>
            </div>
        </div>

        <!-- متابعة التحديثات -->
        <div class="follow-section">
            <h3>تابعنا علي جروب وإدارة البرنامج</h3>
            <p>لمتابعة كل جديد أو تحديث يحصل في التطبيق هتلاقي رسالة هنا في القناة أو الجروب تبع الواتساب</p>
            <div class="social-links">
                <button class="whatsapp-btn" onclick="openWhatsApp()">
                    <i class="fab fa-whatsapp"></i>
                    جروب الواتساب
                </button>
                <button class="channel-btn" onclick="openChannel()">
                    <i class="fas fa-bullhorn"></i>
                    قناة التحديثات
                </button>
            </div>
        </div>
    </main>

    <script>
        // التحقق من تسجيل الدخول
        function checkLogin() {
            const isLoggedIn = localStorage.getItem('isLoggedIn');
            const userEmail = localStorage.getItem('userEmail');
            const userName = localStorage.getItem('userName');
            const userPicture = localStorage.getItem('userPicture');

            if (!isLoggedIn || isLoggedIn !== 'true') {
                window.location.href = 'login-new.html';
                return;
            }

            // عرض بيانات المستخدم
            if (userEmail) {
                document.getElementById('userEmail').textContent = userEmail;
            }

            if (userName) {
                document.getElementById('userName').textContent = userName;
            } else {
                document.getElementById('userName').textContent = userEmail ? userEmail.split('@')[0] : 'مستخدم';
            }

            if (userPicture) {
                document.getElementById('userAvatar').src = userPicture;
            }
        }

        // تسجيل الخروج
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                localStorage.removeItem('isLoggedIn');
                localStorage.removeItem('userEmail');
                localStorage.removeItem('userName');
                localStorage.removeItem('userPicture');
                localStorage.removeItem('loginMethod');
                
                // تأثير تحميل
                document.body.style.transition = 'opacity 0.5s ease-out';
                document.body.style.opacity = '0.3';
                
                setTimeout(() => {
                    window.location.href = 'home.html';
                }, 800);
            }
        }

        // فتح جروب الواتساب
        function openWhatsApp() {
            window.open('https://whatsapp.com/channel/0029Vb6YfqVEKyZOs9EUJK1m', '_blank');
        }

        // فتح قناة التحديثات
        function openChannel() {
            window.open('https://whatsapp.com/channel/0029Vb6YfqVEKyZOs9EUJK1m', '_blank');
        }

        // تشغيل التحقق عند تحميل الصفحة
        window.onload = checkLogin;
    </script>
</body>
</html>
