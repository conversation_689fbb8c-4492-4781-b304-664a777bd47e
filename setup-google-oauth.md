# إعد<PERSON> Google OAuth للحصول على حسابا<PERSON> Google الحقيقية

## المشكلة الحالية:
Google Sign-In يحتاج إعداد صحيح للوصول لحسابات Google الحقيقية في الجهاز.

## الحل الصحيح:

### 1. إنشاء مشروع في Google Cloud Console
1. اذهب إلى: https://console.cloud.google.com/
2. أنشئ مشروع جديد أو اختر مشروع موجود
3. اسم المشروع: "ملكوتك آتٍ - Kingdom Coming"

### 2. تفعيل Google Identity API
1. اذهب إلى "APIs & Services" > "Library"
2. ابحث عن "Google Identity"
3. اضغط على "Google Identity" واضغط "Enable"

### 3. إنشاء OAuth 2.0 Client ID
1. اذه<PERSON> إلى "APIs & Services" > "Credentials"
2. اضغط "Create Credentials" > "OAuth 2.0 Client ID"
3. اختر "Web application"
4. اسم التطبيق: "ملكوتك آتٍ"
5. أضف النطاقات المسموحة في "Authorized JavaScript origins":
   - `http://localhost`
   - `http://localhost:3000`
   - `http://localhost:8000`
   - `http://localhost:8080`
   - `file://`
6. أضف في "Authorized redirect URIs":
   - `http://localhost/login.html`
   - `http://localhost:3000/login.html`

### 4. إعداد OAuth Consent Screen
1. اذهب إلى "APIs & Services" > "OAuth consent screen"
2. اختر "External" (للاختبار)
3. املأ المعلومات المطلوبة:
   - App name: "ملكوتك آتٍ"
   - User support email: إيميلك
   - Developer contact information: إيميلك
4. أضف النطاقات (Scopes):
   - `email`
   - `profile`
   - `openid`

### 5. تحديث الكود
بعد الحصول على Client ID الجديد، استبدل في ملف `login.html`:

```javascript
client_id: 'YOUR_NEW_CLIENT_ID_HERE.apps.googleusercontent.com',
```

### 6. اختبار النظام
1. احفظ الملفات
2. افتح `login.html` في المتصفح
3. اضغط على زر Google Sign-In
4. ستظهر نافذة اختيار الحساب الحقيقية من Google
5. اختر حسابك واستمتع!

## ملاحظات مهمة:

### للاختبار المحلي:
- تأكد من إضافة `file://` في Authorized JavaScript origins
- أو استخدم خادم محلي مثل `http://localhost:3000`

### للنشر الحقيقي:
- أضف نطاقك الحقيقي مثل `https://yourdomain.com`
- غيّر OAuth consent screen إلى "Internal" إذا كان للاستخدام الداخلي

### استكشاف الأخطاء:
- **Error 400: invalid_request**: تحقق من Client ID والنطاقات المسموحة
- **Error 403: access_denied**: تحقق من OAuth consent screen
- **Popup blocked**: تأكد من السماح للنوافذ المنبثقة

## الخطوات السريعة (5 دقائق):
1. اذهب إلى Google Cloud Console
2. أنشئ مشروع جديد
3. فعّل Google Identity API
4. أنشئ OAuth Client ID
5. أضف `file://` في JavaScript origins
6. انسخ Client ID الجديد
7. استبدله في الكود
8. اختبر النظام!

بعد هذا الإعداد، ستحصل على:
✅ حسابات Google الحقيقية من الجهاز
✅ نافذة اختيار الحساب الأصلية من Google
✅ صور وأسماء المستخدمين الحقيقية
✅ تسجيل دخول آمن 100%
