<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - ملكوتك آتٍ</title>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-gold: #D4AF37;
            --primary-dark: #B8860B;
            --secondary-blue: #1E3A8A;
            --text-dark: #1F2937;
            --text-light: #6B7280;
            --white: #FFFFFF;
            --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: var(--gradient-bg);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            direction: rtl;
            position: relative;
            overflow-x: hidden;
        }



        /* زر العودة */
        .back-button {
            position: absolute;
            top: 30px;
            right: 30px;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            color: var(--white);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            width: 60px;
            height: 60px;
            cursor: pointer;
            font-size: 20px;
            transition: all 0.3s ease;
            z-index: 100;
        }

        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        /* حاوية تسجيل الدخول */
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 30px;
            padding: 50px 40px;
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
            width: 100%;
            max-width: 450px;
            text-align: center;
            border: 2px solid rgba(255, 255, 255, 0.3);
            animation: slideIn 0.8s ease-out;
            position: relative;
            overflow: hidden;
        }



        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }



        /* الشعار */
        .logo {
            width: 90px;
            height: 90px;
            margin: 0 auto 25px;
            background: linear-gradient(135deg, var(--primary-gold), var(--primary-dark));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 15px 35px rgba(212, 175, 55, 0.4);

            position: relative;
            z-index: 2;
        }

        .logo i {
            font-size: 45px;
            color: var(--white);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }



        .login-title {
            font-family: 'Amiri', serif;
            font-size: 2rem;
            color: var(--secondary-blue);
            margin-bottom: 35px;
            font-weight: 700;
            position: relative;
            z-index: 2;
        }

        /* زر Google */
        .google-btn {
            width: 100%;
            background: var(--white);
            border: 3px solid #dadce0;
            border-radius: 18px;
            padding: 18px 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.4s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            color: #3c4043;
            margin-bottom: 25px;
            font-weight: 600;
            position: relative;
            overflow: hidden;
            z-index: 2;
        }



        .google-btn:hover {
            background: #f8f9fa;
            border-color: var(--primary-gold);
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .google-logo {
            width: 24px;
            height: 24px;
        }

        /* عرض الحساب المختار */
        .selected-account {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border: 3px solid var(--primary-gold);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 25px;
            display: none;
            animation: fadeIn 0.5s ease-out;
            position: relative;
            z-index: 2;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: scale(0.9); }
            to { opacity: 1; transform: scale(1); }
        }

        .account-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            padding: 15px;
            background: var(--white);
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .account-picture {
            width: 55px;
            height: 55px;
            border-radius: 50%;
            border: 3px solid var(--primary-gold);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .account-details {
            flex: 1;
            margin: 0 15px;
            text-align: right;
        }

        .account-name {
            font-weight: 700;
            color: var(--text-dark);
            font-size: 1.1rem;
            margin-bottom: 5px;
        }

        .account-email {
            color: var(--text-light);
            font-size: 0.95rem;
        }

        .change-btn {
            background: linear-gradient(135deg, var(--primary-gold), var(--primary-dark));
            color: var(--white);
            border: none;
            border-radius: 12px;
            width: 45px;
            height: 45px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .change-btn:hover {
            transform: scale(1.1) rotate(180deg);
        }

        .proceed-btn {
            width: 100%;
            background: linear-gradient(135deg, #10b981, #059669);
            color: var(--white);
            border: none;
            border-radius: 18px;
            padding: 18px;
            font-size: 1.2rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.4s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
        }

        .proceed-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(16, 185, 129, 0.6);
        }

        /* نافذة الترحيب */
        .welcome-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(10px);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            animation: fadeIn 0.3s ease-out;
        }

        .welcome-modal-content {
            background: var(--white);
            border-radius: 25px;
            width: 90%;
            max-width: 500px;
            padding: 40px;
            text-align: center;
            position: relative;
            animation: slideUp 0.4s ease-out;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
        }

        @keyframes slideUp {
            from {
                transform: translateY(50px) scale(0.9);
                opacity: 0;
            }
            to {
                transform: translateY(0) scale(1);
                opacity: 1;
            }
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid var(--primary-gold);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .welcome-header h3 {
            color: var(--secondary-blue);
            font-family: 'Amiri', serif;
            font-size: 1.8rem;
            margin-bottom: 25px;
            font-weight: 700;
        }

        .welcome-message {
            color: var(--text-dark);
            font-size: 1.2rem;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .registration-note {
            color: var(--text-light);
            font-size: 1rem;
            margin-bottom: 30px;
            font-style: italic;
            background: rgba(212, 175, 55, 0.1);
            padding: 10px;
            border-radius: 10px;
        }

        .email-input {
            width: 100%;
            padding: 18px;
            border: 3px solid #e5e7eb;
            border-radius: 15px;
            font-size: 1.1rem;
            margin-bottom: 20px;
            direction: rtl;
            text-align: right;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .email-input:focus {
            outline: none;
            border-color: var(--primary-gold);
            box-shadow: 0 0 20px rgba(212, 175, 55, 0.3);
        }

        .proceed-email-btn {
            width: 100%;
            background: linear-gradient(135deg, var(--primary-gold), var(--primary-dark));
            color: var(--white);
            border: none;
            border-radius: 15px;
            padding: 18px;
            font-size: 1.2rem;
            font-weight: 700;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            transition: all 0.3s ease;
        }

        .proceed-email-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(212, 175, 55, 0.5);
        }

        .close-welcome-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: var(--text-light);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .close-welcome-btn:hover {
            background: #f3f4f6;
            color: var(--text-dark);
            transform: scale(1.1);
        }

        /* استجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            .login-container {
                padding: 35px 25px;
                margin: 10px;
            }
            
            .login-title {
                font-size: 1.6rem;
            }
            
            .google-btn {
                padding: 15px 20px;
                font-size: 1rem;
            }
            
            .back-button {
                top: 20px;
                right: 20px;
                width: 50px;
                height: 50px;
                font-size: 18px;
            }
        }
    </style>
</head>
<body>


    <!-- زر العودة -->
    <button class="back-button" onclick="goBack()">
        <i class="fas fa-arrow-right"></i>
    </button>

    <!-- حاوية تسجيل الدخول -->
    <div class="login-container">
        <div class="logo">
            <i class="fas fa-cross"></i>
        </div>
        
        <h2 class="login-title">تسجيل الدخول - ملكوتك آتٍ</h2>

        <!-- زر تسجيل الدخول بجوجل -->
        <button onclick="showEmailPrompt()" class="google-btn">
            <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" class="google-logo">
            تسجيل الدخول باستخدام Google
        </button>

        <!-- عرض الحساب المختار -->
        <div id="selected-account" class="selected-account">
            <div class="account-info">
                <img id="account-picture" src="" alt="صورة الحساب" class="account-picture">
                <div class="account-details">
                    <div id="account-name" class="account-name"></div>
                    <div id="account-email" class="account-email"></div>
                </div>
                <button onclick="changeAccount()" class="change-btn">
                    <i class="fas fa-exchange-alt"></i>
                </button>
            </div>
            <button onclick="proceedToApp()" class="proceed-btn">
                <i class="fas fa-rocket"></i>
                دخول للبرنامج
            </button>
        </div>
    </div>

    <!-- نافذة الترحيب -->
    <div id="welcome-modal" class="welcome-modal">
        <div class="welcome-modal-content">
            <div class="welcome-header">
                <div class="loading-spinner"></div>
                <h3>مرحباً بك في ملكوتك آتٍ</h3>
            </div>
            <div class="welcome-body">
                <p class="welcome-message">أدخل بريدك الإلكتروني للدخول</p>
                <p class="registration-note">(التسجيل يحتاج مرة واحدة فقط لأول مرة)</p>
                
                <div class="email-input-section">
                    <input type="email" id="user-email" placeholder="أدخل إيميل Google الخاص بك" class="email-input" required>
                    <button onclick="proceedWithEmail()" class="proceed-email-btn">
                        <i class="fab fa-google"></i>
                        متابعة
                    </button>
                </div>
                
                <button onclick="closeWelcomeModal()" class="close-welcome-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>

    <script>
        window.tempUserData = null;

        function goBack() {
            window.location.href = 'home.html';
        }

        function showEmailPrompt() {
            document.getElementById('welcome-modal').style.display = 'flex';
            setTimeout(() => {
                document.getElementById('user-email').focus();
            }, 400);
        }

        function proceedWithEmail() {
            const email = document.getElementById('user-email').value;
            if (email && email.includes('@')) {
                const name = email.split('@')[0].replace(/\./g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                const userData = {
                    email: email,
                    name: name,
                    picture: `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=4285f4&color=fff&size=128&font-size=0.6`
                };
                
                closeWelcomeModal();
                showSelectedAccount(userData);
                window.tempUserData = userData;
            } else {
                alert('يرجى إدخال إيميل صحيح');
            }
        }

        function closeWelcomeModal() {
            document.getElementById('welcome-modal').style.display = 'none';
        }

        function showSelectedAccount(userData) {
            document.getElementById('account-picture').src = userData.picture;
            document.getElementById('account-name').textContent = userData.name;
            document.getElementById('account-email').textContent = userData.email;
            document.getElementById('selected-account').style.display = 'block';
        }

        function changeAccount() {
            document.getElementById('selected-account').style.display = 'none';
            window.tempUserData = null;
        }

        function proceedToApp() {
            if (window.tempUserData) {
                const userData = window.tempUserData;
                
                localStorage.setItem('userEmail', userData.email);
                localStorage.setItem('userName', userData.name);
                localStorage.setItem('userPicture', userData.picture);
                localStorage.setItem('isLoggedIn', 'true');
                localStorage.setItem('loginMethod', 'google');

                // تأثير تحميل
                document.body.style.transition = 'opacity 0.5s ease-out';
                document.body.style.opacity = '0.3';
                
                setTimeout(() => {
                    window.location.href = 'dashboard-new.html';
                }, 800);
            }
        }

        // التحقق من تسجيل الدخول المسبق
        window.onload = function() {
            if (localStorage.getItem('isLoggedIn') === 'true') {
                window.location.href = 'dashboard-new.html';
            }
        }
    </script>
</body>
</html>
