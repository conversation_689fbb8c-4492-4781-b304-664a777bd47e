/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-gold: #D4AF37;
    --primary-dark: #B8860B;
    --secondary-blue: #1E3A8A;
    --light-blue: #3B82F6;
    --text-dark: #1F2937;
    --text-light: #6B7280;
    --background: #FAFAFA;
    --white: #FFFFFF;
    --shadow: rgba(0, 0, 0, 0.1);
    --shadow-hover: rgba(0, 0, 0, 0.15);
}

body {
    font-family: 'Cairo', '<PERSON>i', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: var(--text-dark);
    direction: rtl;
    text-align: right;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.app-container {
    width: 100%;
    max-width: 450px;
    margin: 0 auto;
    animation: fadeIn 0.8s ease-in-out;
}

.welcome-card {
    background: var(--white);
    border-radius: 24px;
    padding: 40px 30px;
    box-shadow: 0 20px 40px var(--shadow);
    text-align: center;
    position: relative;
    overflow: hidden;
    animation: slideUp 0.6s ease-out;
}

.welcome-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-gold), var(--primary-dark));
}

.logo-container {
    margin-bottom: 30px;
}

.logo {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    background: linear-gradient(135deg, var(--primary-gold), var(--primary-dark));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 20px rgba(212, 175, 55, 0.3);
    animation: pulse 2s infinite;
}

.logo i {
    font-size: 40px;
    color: var(--white);
}

.app-title {
    font-family: 'Amiri', serif;
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--secondary-blue);
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.app-subtitle {
    font-size: 0.95rem;
    color: var(--text-light);
    margin-bottom: 15px;
}

.designer-name {
    font-family: 'Cairo', sans-serif;
    font-size: 1.1rem;
    font-weight: 800;
    color: var(--primary-gold);
    margin-bottom: 35px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.8px;
    background: linear-gradient(135deg, var(--primary-gold), var(--primary-dark));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    padding: 8px 16px;
    border-radius: 20px;
    background-color: rgba(212, 175, 55, 0.1);
    border: 1px solid rgba(212, 175, 55, 0.3);
    display: inline-block;
    transition: all 0.3s ease;
}

.designer-name:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(212, 175, 55, 0.2);
    background-color: rgba(212, 175, 55, 0.15);
}

/* إعلان الافتتاح */
.launch-announcement {
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    color: var(--white);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 25px;
    text-align: center;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(30, 58, 138, 0.3);
}

.launch-announcement::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 3s infinite;
}

.announcement-icon {
    font-size: 48px;
    margin-bottom: 15px;
    color: var(--primary-gold);
}

.announcement-title {
    font-family: 'Amiri', serif;
    font-size: 1.3rem;
    margin-bottom: 15px;
    font-weight: 600;
}

.launch-date {
    font-family: 'Amiri', serif;
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--primary-gold);
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    letter-spacing: 2px;
}

.wait-text {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 500;
}

/* الشريط المتحرك */
.moving-banner {
    background: linear-gradient(90deg, var(--primary-gold), var(--primary-dark), var(--primary-gold));
    color: var(--white);
    padding: 12px 0;
    margin-bottom: 25px;
    border-radius: 25px;
    overflow: hidden;
    position: relative;
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

.banner-content {
    display: flex;
    animation: scroll 20s linear infinite;
    white-space: nowrap;
}

.banner-content span {
    padding: 0 40px;
    font-weight: 600;
    font-size: 0.95rem;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

@keyframes scroll {
    0% { transform: translateX(0); }
    100% { transform: translateX(-50%); }
}

.verse-container {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-radius: 16px;
    padding: 25px;
    margin-bottom: 35px;
    border-right: 4px solid var(--primary-gold);
    animation: fadeInUp 0.8s ease-out 0.3s both;
}

.verse-text {
    font-family: 'Amiri', serif;
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-dark);
    margin-bottom: 12px;
    font-weight: 400;
}

.verse-reference {
    font-size: 0.9rem;
    color: var(--text-light);
    font-weight: 600;
}

.journey-text {
    font-size: 1rem;
    color: var(--text-light);
    margin-bottom: 30px;
    font-weight: 500;
}

.login-button {
    width: 100%;
    background: linear-gradient(135deg, var(--primary-gold), var(--primary-dark));
    color: var(--white);
    border: none;
    border-radius: 16px;
    padding: 18px 24px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 20px rgba(212, 175, 55, 0.3);
    font-family: 'Cairo', sans-serif;
    animation: fadeInUp 0.8s ease-out 0.5s both;
}

.login-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 25px rgba(212, 175, 55, 0.4);
}

.login-button:active {
    transform: translateY(0);
}

.login-button i {
    margin-left: 10px;
}

.qr-section {
    margin: 30px 0;
    padding: 20px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 12px;
    border: 1px dashed var(--primary-gold);
    animation: fadeInUp 0.8s ease-out 0.7s both;
}

.qr-title {
    font-size: 0.9rem;
    color: var(--text-dark);
    margin-bottom: 15px;
    font-weight: 600;
}

.qr-code {
    width: 120px;
    height: 120px;
    margin: 0 auto;
    background: var(--white);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px var(--shadow);
}

.qr-code i {
    font-size: 60px;
    color: var(--primary-gold);
}

.footer {
    margin-top: 35px;
    padding-top: 25px;
    border-top: 1px solid #e5e7eb;
    text-align: center;
    animation: fadeInUp 0.8s ease-out 0.9s both;
}

.designer-credit {
    font-size: 0.85rem;
    color: var(--text-light);
    margin-bottom: 8px;
}

.copyright {
    font-size: 0.8rem;
    color: var(--text-light);
}

/* الحركات والتأثيرات */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 8px 20px rgba(212, 175, 55, 0.3);
    }
    50% {
        box-shadow: 0 8px 25px rgba(212, 175, 55, 0.5);
    }
    100% {
        box-shadow: 0 8px 20px rgba(212, 175, 55, 0.3);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* التجاوب مع الشاشات الصغيرة */
@media (max-width: 480px) {
    body {
        padding: 15px;
    }
    
    .welcome-card {
        padding: 30px 20px;
    }
    
    .app-title {
        font-size: 1.8rem;
    }
    
    .verse-text {
        font-size: 1rem;
    }
    
    .login-button {
        font-size: 1rem;
        padding: 16px 20px;
    }

    .designer-name {
        font-size: 1rem;
        padding: 6px 12px;
        letter-spacing: 0.5px;
    }
}

/* نموذج التسجيل السريع */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    animation: fadeIn 0.3s ease-in-out;
}

.modal-content {
    background-color: var(--white);
    margin: 2% auto;
    padding: 0;
    border-radius: 20px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    animation: slideUp 0.3s ease-out;
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-gold), var(--primary-dark));
    color: var(--white);
    padding: 20px 30px;
    border-radius: 20px 20px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    font-family: 'Amiri', serif;
    font-size: 1.5rem;
    margin: 0;
}

.close {
    color: var(--white);
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: #f0f0f0;
}

#registrationForm {
    padding: 30px;
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.form-group {
    flex: 1;
}

.form-group.full-width {
    flex: 100%;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-dark);
    font-weight: 600;
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    font-size: 0.95rem;
    transition: border-color 0.3s ease;
    direction: rtl;
    text-align: right;
    font-family: 'Cairo', sans-serif;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-gold);
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.form-group select {
    cursor: pointer;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.submit-btn {
    width: 100%;
    background: linear-gradient(135deg, #25D366, #128C7E);
    color: var(--white);
    border: none;
    border-radius: 12px;
    padding: 18px 24px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Cairo', sans-serif;
    margin-top: 10px;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(37, 211, 102, 0.4);
}

.submit-btn i {
    margin-left: 10px;
    font-size: 1.2rem;
}

/* تجاوب النموذج مع الشاشات الصغيرة */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 5% auto;
    }

    .form-row {
        flex-direction: column;
        gap: 10px;
    }

    .modal-header {
        padding: 15px 20px;
    }

    .modal-header h2 {
        font-size: 1.3rem;
    }

    #registrationForm {
        padding: 20px;
    }
}
