<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نموذج التسجيل - ملكوتك آتٍ</title>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-gold: #D4AF37;
            --primary-dark: #B8860B;
            --secondary-blue: #1E3A8A;
            --text-dark: #1F2937;
            --text-light: #6B7280;
            --white: #FFFFFF;
            --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: var(--gradient-bg);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
            position: relative;
            overflow-x: hidden;
        }

        /* تأثيرات الخلفية */
        .background-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .floating-shape {
            position: absolute;
            opacity: 0.1;
            animation: float 12s infinite ease-in-out;
        }

        .floating-shape:nth-child(1) { 
            top: 10%; left: 10%; 
            width: 60px; height: 60px; 
            background: var(--primary-gold); 
            border-radius: 50%; 
            animation-delay: 0s; 
        }
        .floating-shape:nth-child(2) { 
            top: 20%; right: 15%; 
            width: 40px; height: 40px; 
            background: var(--secondary-blue); 
            border-radius: 20%; 
            animation-delay: 2s; 
        }
        .floating-shape:nth-child(3) { 
            bottom: 30%; left: 20%; 
            width: 50px; height: 50px; 
            background: #10b981; 
            border-radius: 30%; 
            animation-delay: 4s; 
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-40px) rotate(180deg); }
        }

        /* زر العودة */
        .back-button {
            position: fixed;
            top: 30px;
            right: 30px;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            color: var(--white);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            width: 60px;
            height: 60px;
            cursor: pointer;
            font-size: 20px;
            transition: all 0.3s ease;
            z-index: 100;
        }

        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        /* الحاوية الرئيسية */
        .form-container {
            max-width: 800px;
            margin: 50px auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 30px;
            padding: 50px 40px;
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            animation: slideIn 0.8s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* الهيدر */
        .form-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, var(--primary-gold), var(--primary-dark));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 15px 35px rgba(212, 175, 55, 0.4);
            animation: pulse 3s infinite;
        }

        .logo i {
            font-size: 40px;
            color: var(--white);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .form-title {
            font-family: 'Amiri', serif;
            font-size: 2.2rem;
            color: var(--secondary-blue);
            margin-bottom: 15px;
            font-weight: 700;
        }

        .form-subtitle {
            font-size: 1.1rem;
            color: var(--text-light);
            line-height: 1.6;
        }

        /* النموذج */
        .registration-form {
            display: grid;
            gap: 25px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-group label {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-group label i {
            color: var(--primary-gold);
            font-size: 1rem;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            padding: 15px 18px;
            border: 3px solid #e5e7eb;
            border-radius: 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: var(--white);
            direction: rtl;
            text-align: right;
            font-family: 'Cairo', sans-serif;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--primary-gold);
            box-shadow: 0 0 20px rgba(212, 175, 55, 0.3);
            transform: translateY(-2px);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        /* زر الإرسال */
        .submit-btn {
            background: linear-gradient(135deg, var(--primary-gold), var(--primary-dark));
            color: var(--white);
            border: none;
            border-radius: 18px;
            padding: 20px 40px;
            font-size: 1.3rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.4s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            margin-top: 20px;
            box-shadow: 0 10px 30px rgba(212, 175, 55, 0.4);
            position: relative;
            overflow: hidden;
        }

        .submit-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s;
        }

        .submit-btn:hover::before {
            left: 100%;
        }

        .submit-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(212, 175, 55, 0.6);
        }

        /* رسالة النجاح */
        .success-message {
            background: linear-gradient(135deg, #10b981, #059669);
            color: var(--white);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-top: 20px;
            display: none;
            animation: fadeIn 0.5s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: scale(0.9); }
            to { opacity: 1; transform: scale(1); }
        }

        .success-message i {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .success-message h3 {
            font-size: 1.3rem;
            margin-bottom: 8px;
        }

        .success-message p {
            font-size: 1rem;
            opacity: 0.9;
        }

        /* تذييل */
        .form-footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid rgba(212, 175, 55, 0.2);
        }

        .designer-credit {
            font-size: 0.9rem;
            color: var(--text-light);
            font-style: italic;
        }

        /* استجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            .form-container {
                margin: 20px auto;
                padding: 30px 20px;
            }
            
            .form-title {
                font-size: 1.8rem;
            }
            
            .form-row {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .submit-btn {
                padding: 18px 30px;
                font-size: 1.1rem;
            }
            
            .back-button {
                top: 20px;
                right: 20px;
                width: 50px;
                height: 50px;
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <!-- تأثيرات الخلفية -->
    <div class="background-animation">
        <div class="floating-shape"></div>
        <div class="floating-shape"></div>
        <div class="floating-shape"></div>
    </div>

    <!-- زر العودة -->
    <button class="back-button" onclick="goBack()">
        <i class="fas fa-arrow-right"></i>
    </button>

    <!-- حاوية النموذج -->
    <div class="form-container">
        <!-- الهيدر -->
        <div class="form-header">
            <div class="logo">
                <i class="fas fa-cross"></i>
            </div>
            <h1 class="form-title">نموذج التسجيل في ملكوتك آتٍ</h1>
            <p class="form-subtitle">
                املأ البيانات التالية للانضمام إلى رحلتنا الروحية المباركة<br>
                وتلقي الرسائل اليومية المليئة بالنعم والبركات
            </p>
        </div>

        <!-- النموذج -->
        <form class="registration-form" onsubmit="submitForm(event)">
            <div class="form-row">
                <div class="form-group">
                    <label for="firstName">
                        <i class="fas fa-user"></i>
                        الاسم الأول:
                    </label>
                    <input type="text" id="firstName" name="firstName" required>
                </div>
                <div class="form-group">
                    <label for="lastName">
                        <i class="fas fa-user"></i>
                        الاسم الأخير:
                    </label>
                    <input type="text" id="lastName" name="lastName" required>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="email">
                        <i class="fas fa-envelope"></i>
                        البريد الإلكتروني:
                    </label>
                    <input type="email" id="email" name="email" required>
                </div>
                <div class="form-group">
                    <label for="phone">
                        <i class="fas fa-phone"></i>
                        رقم الهاتف:
                    </label>
                    <input type="tel" id="phone" name="phone" required>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="age">
                        <i class="fas fa-calendar"></i>
                        العمر:
                    </label>
                    <select id="age" name="age" required>
                        <option value="">اختر العمر</option>
                        <option value="أقل من 18">أقل من 18</option>
                        <option value="18-25">18-25</option>
                        <option value="26-35">26-35</option>
                        <option value="36-45">36-45</option>
                        <option value="46-55">46-55</option>
                        <option value="أكثر من 55">أكثر من 55</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="church">
                        <i class="fas fa-church"></i>
                        الكنيسة:
                    </label>
                    <input type="text" id="church" name="church">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="city">
                        <i class="fas fa-map-marker-alt"></i>
                        المدينة:
                    </label>
                    <input type="text" id="city" name="city">
                </div>
                <div class="form-group">
                    <label for="country">
                        <i class="fas fa-globe"></i>
                        البلد:
                    </label>
                    <input type="text" id="country" name="country">
                </div>
            </div>

            <div class="form-group full-width">
                <label for="notes">
                    <i class="fas fa-comment"></i>
                    ملاحظات إضافية أو طلبات خاصة:
                </label>
                <textarea id="notes" name="notes" placeholder="اكتب أي ملاحظات أو طلبات خاصة هنا..."></textarea>
            </div>

            <button type="submit" class="submit-btn">
                <i class="fas fa-paper-plane"></i>
                إرسال النموذج والانضمام للبرنامج
            </button>
        </form>

        <!-- رسالة النجاح -->
        <div id="success-message" class="success-message">
            <i class="fas fa-check-circle"></i>
            <h3>تم إرسال النموذج بنجاح!</h3>
            <p>شكراً لانضمامك إلى عائلة ملكوتك آتٍ. ستصلك رسالة تأكيد قريباً.</p>
        </div>

        <!-- التذييل -->
        <div class="form-footer">
            <p class="designer-credit">تصميم وتطوير: بيشوي مراد</p>
        </div>
    </div>

    <script>
        function goBack() {
            window.location.href = 'home.html';
        }

        function submitForm(event) {
            event.preventDefault();
            
            // جمع بيانات النموذج
            const formData = new FormData(event.target);
            const data = Object.fromEntries(formData);
            
            // محاكاة إرسال البيانات
            console.log('بيانات النموذج:', data);
            
            // إخفاء النموذج وإظهار رسالة النجاح
            event.target.style.display = 'none';
            document.getElementById('success-message').style.display = 'block';
            
            // التوجيه لصفحة تسجيل الدخول بعد 3 ثوانٍ
            setTimeout(() => {
                window.location.href = 'login-new.html';
            }, 3000);
        }

        // تأثير تحميل الصفحة
        window.addEventListener('load', function() {
            document.body.style.opacity = '0';
            document.body.style.transition = 'opacity 0.5s ease-in-out';
            setTimeout(() => {
                document.body.style.opacity = '1';
            }, 100);
        });
    </script>
</body>
</html>
