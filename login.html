<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - ملكوتك آتٍ</title>
    <link rel="icon" type="image/svg+xml" href="public/logo.svg">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Sign-In -->
    <script src="https://accounts.google.com/gsi/client" async defer></script>


    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-gold: #D4AF37;
            --primary-dark: #B8860B;
            --secondary-blue: #1E3A8A;
            --text-dark: #1F2937;
            --text-light: #6B7280;
            --white: #FFFFFF;
            --shadow: rgba(0, 0, 0, 0.1);
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            direction: rtl;
        }

        .login-container {
            background: var(--white);
            border-radius: 24px;
            padding: 40px 30px;
            box-shadow: 0 20px 40px var(--shadow);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        .back-button {
            position: absolute;
            top: 20px;
            right: 20px;
            background: var(--primary-gold);
            color: var(--white);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            font-size: 18px;
            transition: all 0.3s ease;
        }

        .back-button:hover {
            background: var(--primary-dark);
            transform: scale(1.1);
        }

        .logo {
            width: 60px;
            height: 60px;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, var(--primary-gold), var(--primary-dark));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo i {
            font-size: 30px;
            color: var(--white);
        }

        .login-title {
            font-family: 'Amiri', serif;
            font-size: 1.8rem;
            color: var(--secondary-blue);
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: right;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--text-dark);
            font-weight: 600;
        }

        .form-group input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
            direction: rtl;
            text-align: right;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--primary-gold);
        }

        .login-btn {
            width: 100%;
            background: linear-gradient(135deg, var(--primary-gold), var(--primary-dark));
            color: var(--white);
            border: none;
            border-radius: 12px;
            padding: 15px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(212, 175, 55, 0.4);
        }

        .forgot-password {
            margin-top: 20px;
            color: var(--text-light);
            text-decoration: none;
            font-size: 0.9rem;
        }

        .forgot-password:hover {
            color: var(--primary-gold);
        }



        .toast-message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 10px;
            animation: slideInRight 0.3s ease-out;
            max-width: 400px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .toast-message.success {
            background: linear-gradient(135deg, #10b981, #059669);
        }

        .toast-message.error {
            background: linear-gradient(135deg, #ef4444, #dc2626);
        }

        .toast-message i {
            font-size: 18px;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .google-login-section {
            margin-bottom: 25px;
        }

        .login-instruction {
            text-align: center;
            color: var(--text-light);
            margin-bottom: 20px;
            font-size: 0.95rem;
        }

        .google-signin-container {
            width: 100%;
            margin-bottom: 25px;
            display: flex;
            justify-content: center;
        }

        .google-signin-container > div {
            width: 100% !important;
        }

        .google-signin-container iframe {
            width: 100% !important;
        }



        .selected-account {
            background: #f8fafc;
            border: 2px solid var(--primary-gold);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }

        .account-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
            padding: 10px;
            background: var(--white);
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .account-picture {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: 2px solid var(--primary-gold);
        }

        .account-details {
            flex: 1;
            margin: 0 15px;
            text-align: right;
        }

        .account-name {
            font-weight: 600;
            color: var(--text-dark);
            font-size: 1rem;
            margin-bottom: 4px;
        }

        .account-email {
            color: var(--text-light);
            font-size: 0.9rem;
        }

        .change-account-btn {
            background: var(--primary-gold);
            color: var(--white);
            border: none;
            border-radius: 8px;
            width: 40px;
            height: 40px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .change-account-btn:hover {
            background: var(--primary-dark);
            transform: scale(1.1);
        }

        .proceed-btn {
            width: 100%;
            background: linear-gradient(135deg, #10b981, #059669);
            color: var(--white);
            border: none;
            border-radius: 12px;
            padding: 15px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .proceed-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(16, 185, 129, 0.4);
        }

        .google-signin-btn {
            width: 100%;
            background: var(--white);
            border: 2px solid #dadce0;
            border-radius: 12px;
            padding: 15px 20px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            color: #3c4043;
            font-family: 'Cairo', sans-serif;
        }

        .google-signin-btn:hover {
            background: #f8f9fa;
            border-color: #c1c7cd;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .google-logo {
            width: 20px;
            height: 20px;
        }

        .account-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            animation: fadeIn 0.3s ease-out;
        }

        .modal-content {
            background: var(--white);
            border-radius: 16px;
            width: 90%;
            max-width: 400px;
            max-height: 80vh;
            overflow-y: auto;
            animation: slideUp 0.3s ease-out;
        }

        .modal-header {
            padding: 20px 20px 10px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            color: var(--text-dark);
            font-size: 1.2rem;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: var(--text-light);
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background 0.3s ease;
        }

        .close-btn:hover {
            background: #f3f4f6;
        }

        .modal-body {
            padding: 20px;
        }

        .modal-body p {
            margin: 0 0 20px;
            color: var(--text-light);
            text-align: center;
        }

        .account-options {
            margin-bottom: 20px;
        }

        .account-option {
            display: flex;
            align-items: center;
            padding: 12px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .account-option:hover {
            background: #f8f9fa;
            border-color: var(--primary-gold);
        }

        .account-option img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-left: 12px;
        }

        .account-info-modal {
            flex: 1;
            text-align: right;
        }

        .account-name-modal {
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 2px;
        }

        .account-email-modal {
            color: var(--text-light);
            font-size: 0.9rem;
        }

        .manual-email-section {
            border-top: 1px solid #e5e7eb;
            padding-top: 20px;
        }

        .divider-modal {
            text-align: center;
            margin-bottom: 15px;
        }

        .divider-modal span {
            color: var(--text-light);
            font-size: 0.9rem;
        }

        .manual-email-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin-bottom: 10px;
            font-size: 0.9rem;
            direction: rtl;
            text-align: right;
        }

        .manual-email-input:focus {
            outline: none;
            border-color: var(--primary-gold);
        }

        .manual-email-btn {
            width: 100%;
            background: var(--primary-gold);
            color: var(--white);
            border: none;
            border-radius: 8px;
            padding: 10px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .manual-email-btn:hover {
            background: var(--primary-dark);
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from {
                transform: translateY(50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="goBack()">
        <i class="fas fa-arrow-right"></i>
    </button>

    <div class="login-container">
        <div class="logo">
            <i class="fas fa-cross"></i>
        </div>
        
        <h2 class="login-title">تسجيل الدخول - ملكوتك آتٍ</h2>

        <!-- تسجيل الدخول بجوجل فقط -->
        <div class="google-login-section">
            <p class="login-instruction">سجل دخولك باستخدام حساب جوجل الخاص بك</p>

            <!-- عرض الحساب المختار -->
            <div id="selected-account" class="selected-account" style="display: none;">
                <div class="account-info">
                    <img id="account-picture" src="" alt="صورة الحساب" class="account-picture">
                    <div class="account-details">
                        <div id="account-name" class="account-name"></div>
                        <div id="account-email" class="account-email"></div>
                    </div>
                    <button onclick="changeAccount()" class="change-account-btn">
                        <i class="fas fa-exchange-alt"></i>
                    </button>
                </div>
                <button onclick="proceedToApp()" class="proceed-btn">
                    <i class="fas fa-sign-in-alt"></i>
                    دخول للبرنامج
                </button>
            </div>

            <!-- زر تسجيل الدخول بجوجل -->
            <div id="google-signin-button" class="google-signin-container"></div>
        </div>
    </div>

    <script>
        // متغير لحفظ بيانات المستخدم مؤقتاً
        window.tempUserData = null;

        // إعداد Google Sign-In
        window.onload = function() {
            // التحقق من تسجيل الدخول المسبق
            if (localStorage.getItem('isLoggedIn') === 'true') {
                window.location.href = 'dashboard.html';
                return;
            }

            // تهيئة Google Sign-In مع Client ID للاختبار المحلي
            try {
                google.accounts.id.initialize({
                    client_id: '************.apps.googleusercontent.com', // Client ID عام للاختبار
                    callback: handleGoogleSignIn,
                    auto_select: false,
                    cancel_on_tap_outside: false,
                    use_fedcm_for_prompt: true
                });
            } catch (error) {
                console.log('خطأ في تهيئة Google Sign-In:', error);
                // في حالة فشل التهيئة، استخدم زر بديل
                showFallbackGoogleButton();
                return;
            }

            // عرض زر Google Sign-In
            try {
                google.accounts.id.renderButton(
                    document.getElementById('google-signin-button'),
                    {
                        theme: 'outline',
                        size: 'large',
                        text: 'signin_with',
                        shape: 'rectangular',
                        logo_alignment: 'left',
                        width: '100%',
                        locale: 'ar'
                    }
                );

                // إظهار نافذة اختيار الحساب تلقائياً
                setTimeout(() => {
                    google.accounts.id.prompt();
                }, 1000);
            } catch (error) {
                console.log('خطأ في عرض زر Google:', error);
                showFallbackGoogleButton();
            }
        }

        function showFallbackGoogleButton() {
            // زر بديل في حالة فشل Google Sign-In API
            document.getElementById('google-signin-button').innerHTML = `
                <button onclick="openGoogleSignIn()" class="google-signin-btn">
                    <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" class="google-logo">
                    تسجيل الدخول باستخدام Google
                </button>
            `;
        }

        function openGoogleSignIn() {
            // فتح نافذة Google Sign-In في نافذة منفصلة
            const popup = window.open(
                'https://accounts.google.com/oauth/authorize?client_id=************.apps.googleusercontent.com&redirect_uri=' +
                encodeURIComponent(window.location.origin + '/login.html') +
                '&scope=email%20profile&response_type=code&access_type=offline',
                'google-signin',
                'width=500,height=600,scrollbars=yes,resizable=yes'
            );

            // مراقبة النافذة المنبثقة
            const checkClosed = setInterval(() => {
                if (popup.closed) {
                    clearInterval(checkClosed);
                    // محاولة الحصول على بيانات المستخدم من localStorage
                    checkForUserData();
                }
            }, 1000);
        }

        function checkForUserData() {
            // محاولة الحصول على بيانات المستخدم (هذا للاختبار)
            const email = prompt('أدخل إيميل Google الخاص بك:');
            if (email && email.includes('@gmail.com')) {
                const name = email.split('@')[0].replace(/\./g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                const userData = {
                    email: email,
                    name: name,
                    picture: `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=4285f4&color=fff&size=128`
                };

                // إخفاء زر جوجل وإظهار معلومات الحساب
                document.getElementById('google-signin-button').style.display = 'none';
                showSelectedAccount(userData);

                // حفظ بيانات المستخدم مؤقتاً
                window.tempUserData = userData;
            }
        }

        function handleGoogleSignIn(response) {
            try {
                // فك تشفير JWT token من جوجل
                const payload = JSON.parse(atob(response.credential.split('.')[1]));
                const userData = {
                    email: payload.email,
                    name: payload.name,
                    picture: payload.picture,
                    given_name: payload.given_name,
                    family_name: payload.family_name
                };

                console.log('بيانات المستخدم من جوجل:', userData);

                // إخفاء زر جوجل وإظهار معلومات الحساب
                document.getElementById('google-signin-button').style.display = 'none';
                showSelectedAccount(userData);

                // حفظ بيانات المستخدم مؤقتاً
                window.tempUserData = userData;

            } catch (error) {
                console.error('خطأ في تسجيل الدخول بجوجل:', error);
                showErrorMessage('حدث خطأ في تسجيل الدخول بجوجل. يرجى المحاولة مرة أخرى.');
            }
        }

        function goBack() {
            window.location.href = 'index.html';
        }

        // لا نحتاج دالة signInWithGoogle منفصلة لأن الزر سيتم إنشاؤه تلقائياً



        function showSelectedAccount(userData) {
            // إظهار معلومات الحساب المختار
            document.getElementById('account-picture').src = userData.picture;
            document.getElementById('account-name').textContent = userData.name;
            document.getElementById('account-email').textContent = userData.email;
            document.getElementById('selected-account').style.display = 'block';
        }

        function changeAccount() {
            // إخفاء معلومات الحساب وإظهار زر جوجل مرة أخرى
            document.getElementById('selected-account').style.display = 'none';
            document.getElementById('google-signin-button').style.display = 'flex';
            window.tempUserData = null;
        }

        function proceedToApp() {
            if (window.tempUserData) {
                const userData = window.tempUserData;

                // حفظ بيانات المستخدم نهائياً
                localStorage.setItem('userEmail', userData.email);
                localStorage.setItem('userName', userData.name);
                localStorage.setItem('userPicture', userData.picture);
                localStorage.setItem('isLoggedIn', 'true');
                localStorage.setItem('loginMethod', 'google');

                // إظهار رسالة ترحيب
                showSuccessMessage(`مرحباً ${userData.name}! تم تسجيل الدخول بنجاح في ملكوتك آتٍ`);

                // التوجيه للصفحة الرئيسية بعد ثانيتين
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 2000);
            }
        }



        function showSuccessMessage(message) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'toast-message success';
            messageDiv.innerHTML = `
                <i class="fas fa-check-circle"></i>
                <span>${message}</span>
            `;
            document.body.appendChild(messageDiv);

            setTimeout(() => {
                messageDiv.remove();
            }, 4000);
        }

        function showErrorMessage(message) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'toast-message error';
            messageDiv.innerHTML = `
                <i class="fas fa-exclamation-circle"></i>
                <span>${message}</span>
            `;
            document.body.appendChild(messageDiv);

            setTimeout(() => {
                messageDiv.remove();
            }, 4000);
        }

        // تم نقل window.onload للأعلى مع إعداد Google Sign-In
    </script>
</body>
</html>
