# إعد<PERSON> Google Sign-In

## للاستخدام الحقيقي (إنتاج):

### 1. إنشاء مشروع في Google Cloud Console
1. اذهب إلى: https://console.cloud.google.com/
2. أنشئ مشروع جديد أو اختر مشروع موجود
3. فعّل Google+ API أو Google Identity API

### 2. إنشاء OAuth 2.0 Client ID
1. اذهب إلى "APIs & Services" > "Credentials"
2. اضغط "Create Credentials" > "OAuth 2.0 Client ID"
3. اختر "Web application"
4. أضف النطاقات المسموحة:
   - `http://localhost:3000` (للاختبار المحلي)
   - `https://yourdomain.com` (نطاقك الحقيقي)

### 3. تحديث الكود
في ملف `login.html`، ابحث عن هذا السطر:
```javascript
client_id: '1234567890-abcdefghijklmnopqrstuvwxyz.apps.googleusercontent.com',
```

واستبدله بـ Client ID الحقيقي الخاص بك.

## للاختبار المحلي (حالياً):

النظام الحالي يعمل في وضع الاختبار:
1. اضغط "تسجيل الدخول بجوجل"
2. أدخل أي إيميل جوجل صحيح
3. سيتم تسجيل الدخول مباشرة

## المميزات المتوفرة:

### ✅ تسجيل الدخول بجوجل
- زر أنيق بتصميم جوجل الرسمي
- حفظ بيانات المستخدم (الاسم، الإيميل، الصورة)
- تسجيل دخول سريع وآمن

### ✅ تسجيل الدخول بالإيميل والكود
- إرسال كود التحقق للإيميل
- رسالة إيميل مخصصة وجميلة
- إعادة إرسال الكود

### ✅ واجهة موحدة
- خيار "تسجيل الدخول بجوجل" أو "بالإيميل"
- تصميم متناسق وأنيق
- تجربة مستخدم سلسة

## الأمان:

### Google Sign-In:
- آمن 100% - لا نحفظ كلمات مرور
- يستخدم OAuth 2.0 المعيار العالمي
- بيانات المستخدم محمية بواسطة جوجل

### Email Verification:
- كود عشوائي لكل محاولة
- صالح لمدة محدودة
- لا يمكن إعادة استخدامه

## استكشاف الأخطاء:

### مشكلة Google Sign-In:
- تأكد من صحة Client ID
- تحقق من النطاقات المسموحة
- راجع console المتصفح للأخطاء

### مشكلة إرسال الإيميل:
- تحقق من اتصال الإنترنت
- راجع صندوق Spam
- تأكد من صحة عنوان الإيميل

## الدعم:
- وثائق Google Identity: https://developers.google.com/identity
- مجتمع المطورين: https://stackoverflow.com/questions/tagged/google-oauth
