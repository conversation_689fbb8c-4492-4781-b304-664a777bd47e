<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - ملكوتك آتٍ (نسخة اختبار)</title>
    <link rel="icon" type="image/svg+xml" href="public/logo.svg">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-gold: #D4AF37;
            --primary-dark: #B8860B;
            --secondary-blue: #1E3A8A;
            --text-dark: #1F2937;
            --text-light: #6B7280;
            --white: #FFFFFF;
            --shadow: rgba(0, 0, 0, 0.1);
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            direction: rtl;
        }

        .login-container {
            background: var(--white);
            border-radius: 24px;
            padding: 40px 30px;
            box-shadow: 0 20px 40px var(--shadow);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        .back-button {
            position: absolute;
            top: 20px;
            right: 20px;
            background: var(--primary-gold);
            color: var(--white);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            font-size: 18px;
            transition: all 0.3s ease;
        }

        .back-button:hover {
            background: var(--primary-dark);
            transform: scale(1.1);
        }

        .logo {
            width: 60px;
            height: 60px;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, var(--primary-gold), var(--primary-dark));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo i {
            font-size: 30px;
            color: var(--white);
        }

        .login-title {
            font-family: 'Amiri', serif;
            font-size: 1.8rem;
            color: var(--secondary-blue);
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: right;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--text-dark);
            font-weight: 600;
        }

        .form-group input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
            direction: rtl;
            text-align: right;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--primary-gold);
        }

        .login-btn {
            width: 100%;
            background: linear-gradient(135deg, var(--primary-gold), var(--primary-dark));
            color: var(--white);
            border: none;
            border-radius: 12px;
            padding: 15px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(212, 175, 55, 0.4);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .test-notice {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            color: #92400e;
            font-size: 0.9rem;
        }

        .test-notice i {
            color: #f59e0b;
            margin-left: 5px;
        }

        .code-display {
            background: #f0f9ff;
            border: 2px solid #0ea5e9;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 1.2rem;
            font-weight: bold;
            color: #0c4a6e;
            letter-spacing: 2px;
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="goBack()">
        <i class="fas fa-arrow-right"></i>
    </button>

    <div class="login-container">
        <div class="logo">
            <i class="fas fa-cross"></i>
        </div>
        
        <h2 class="login-title">تسجيل الدخول</h2>
        
        <div class="test-notice">
            <i class="fas fa-info-circle"></i>
            هذه نسخة اختبار - الكود سيظهر هنا بدلاً من الإرسال بالإيميل
        </div>
        
        <form onsubmit="handleLogin(event)">
            <div class="form-group">
                <label for="email">البريد الإلكتروني:</label>
                <input type="email" id="email" name="email" required placeholder="أدخل بريدك الإلكتروني">
            </div>
            
            <div id="codeSection" style="display: none;">
                <div class="code-display" id="codeDisplay"></div>
                
                <div class="form-group">
                    <label for="verificationCode">أدخل الكود:</label>
                    <input type="text" id="verificationCode" name="verificationCode" 
                           placeholder="أدخل الكود المعروض أعلاه" maxlength="6" pattern="[0-9]{6}">
                </div>
            </div>
            
            <button type="submit" class="login-btn" id="submitBtn">
                <i class="fas fa-paper-plane"></i>
                إرسال كود التحقق
            </button>
        </form>
    </div>

    <script>
        let generatedCode = '';
        let step = 1; // 1 = email, 2 = code

        function goBack() {
            window.location.href = 'index.html';
        }

        function handleLogin(event) {
            event.preventDefault();
            
            if (step === 1) {
                // مرحلة إرسال الكود
                const email = document.getElementById('email').value;
                
                // توليد كود عشوائي
                generatedCode = Math.floor(100000 + Math.random() * 900000).toString();
                
                // عرض الكود
                document.getElementById('codeDisplay').textContent = `كود التحقق: ${generatedCode}`;
                document.getElementById('codeSection').style.display = 'block';
                
                // تغيير النص والأيقونة
                const submitBtn = document.getElementById('submitBtn');
                submitBtn.innerHTML = '<i class="fas fa-check"></i> تحقق ودخول';
                
                // تعطيل حقل الإيميل
                document.getElementById('email').disabled = true;
                
                step = 2;
                
            } else {
                // مرحلة التحقق من الكود
                const enteredCode = document.getElementById('verificationCode').value;
                
                if (enteredCode === generatedCode) {
                    // حفظ بيانات المستخدم
                    const email = document.getElementById('email').value;
                    localStorage.setItem('userEmail', email);
                    localStorage.setItem('isLoggedIn', 'true');
                    
                    alert('تم التحقق بنجاح! مرحباً بك في ملكوتك آتٍ');
                    
                    // التوجيه للصفحة الرئيسية
                    window.location.href = 'dashboard.html';
                } else {
                    alert('كود التحقق غير صحيح. يرجى المحاولة مرة أخرى.');
                    document.getElementById('verificationCode').value = '';
                }
            }
        }

        // التحقق من تسجيل الدخول عند تحميل الصفحة
        window.onload = function() {
            if (localStorage.getItem('isLoggedIn') === 'true') {
                window.location.href = 'dashboard.html';
            }
        }
    </script>
</body>
</html>
